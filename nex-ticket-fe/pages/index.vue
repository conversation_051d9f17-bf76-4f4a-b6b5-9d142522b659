<script lang="ts" setup>
import { Agreement01Icon, ChartLineData01Icon, CustomerSupportIcon, EuroCircleIcon, EuroSquareIcon, MinusSignSquareIcon, PaymentSuccess02Icon, PlayIcon, PlusSignSquareIcon, StartUp02Icon, Ticket02Icon, WavingHand01Icon } from 'hugeicons-vue'

const festivals = ref([
  'CONCERTS',
  'CLUBBING',
  'FESTIVALS',
  'STUDENT PARTIES',
  'RAVES',
  'CONCERTS',
])

// FAQ state management
const expandedFaq = ref<number | null>(null)

// Define FAQ interface
interface FAQ {
  question: string
  answer: string
}

// Get FAQ data from translations
const { t } = useI18n()
const faqData = computed<FAQ[]>(() => {
  const faqs = t('landing.questions.faqs') as unknown as FAQ[]
  return Array.isArray(faqs) ? faqs : []
})

function toggleFaq(index: number) {
  expandedFaq.value = expandedFaq.value === index ? null : index
}
</script>

<template>
  <div
    class="px-6 md:gap-40 md:mb-40
           flex flex-col justify-center items-center gap-16 w-full"
  >
    <!-- Hero -->
    <div
      class="md:pt-40 md:pb-12 md:gap-20
             flex flex-col text-center justify-center items-center pt-36 pb-6 gap-12 w-auto"
    >
      <dev class="flex flex-col justify-center items-center gap-3 md:gap-9 w-full">
        <p class="md:text-9xl md:leading[5.75rem] text-6xl font-sofia font-[800] text-pie-700 whitespace-pre-line leading-[2.875rem] spacing-mobile-3 spacing-desktop-5">
          {{ $t('landing.hero.title') }}
        </p>
        <p class="md:text-xl-medium text-base-medium text-slate-700 md:w-[27.938rem]">
          {{ $t('landing.hero.description') }}
        </p>
      </dev>
      <dev
        class="md:flex-row md:gap-6 md:w-auto
               flex flex-col gap-3 w-full"
      >
        <!-- Desktop -->
        <NexButton text-key="landing.hero.get_started_for_free" to="/registration" variant="primary" :paddingx="9" :paddingy="6" :second-border="true" border-color="white" text-style="text-xl-bold" class="md:flex hidden" />
        <NexButton text-key="landing.hero.contact_sales" to="/contact" variant="secondary" :paddingx="9" :paddingy="6" :append-icon="WavingHand01Icon" text-style="text-xl-bold" class="md:flex hidden" />
        <!-- Mobile -->
        <NexButton text-key="landing.hero.get_started_for_free" to="/registration" variant="primary" :paddingx="6" :paddingy="4" :second-border="true" border-color="white" text-style="text-base-bold" :full-width="true" class="md:hidden" />
        <NexButton text-key="landing.hero.contact_sales" to="/contact" variant="secondary" :paddingx="6" :paddingy="4" :append-icon="WavingHand01Icon" text-style="text-base-bold" :full-width="true" class="md:hidden" />
      </dev>
    </div>

    <!-- Photo -->
    <div
      class="md:h-[56.938rem]
             h-[37.688rem] md:rounded-[48px] rounded-3xl w-full overflow-hidden shadow-lg relative"
    >
      <img
        src="assets/images/landing_pixels_elevate.jpg" alt="Placeholder" class="w-full h-full object-cover bg-slate-600 md:rounded-[48px] rounded-3xl absolute"
      >
      <div class="absolute bottom-0 md:p-16 p-6 gap-4 flex justify-between md:flex-row flex-col md:items-end">
        <div class="md:pr-11 md:w-1/2 gap-4">
          <h1 class="md:text-3xl-bold text-xl-bold text-white">
            {{ $t('landing.photo.title') }}
          </h1>
          <p class="md:text-lg-medium text-sm-medium text-slate-300">
            {{ $t('landing.photo.description_1') }}
            <span class="underline">{{ $t('landing.photo.description_2') }}</span>
            {{ $t('landing.photo.description_3') }}
          </p>
        </div>
        <div class="flex md:justify-end justify-center md:items-start">
          <NexButton text-key="landing.photo.try" to="/registration" variant="secondary" border-color="slate-300" text-color="slate-300" :paddingx="6" :paddingy="3" text-style="text-lg-bold" class="w-full md:w-auto" />
        </div>
      </div>
    </div>

    <!-- Solution -->
    <div class="flex flex-col w-full gap-12">
      <div class="flex flex-col gap-3 items-center w-full">
        <!-- Chat bubbles -->
        <div class="relative h-[8.375rem] md:w-[33.314rem] w-[22.204rem]">
          <div
            class="md:py-3 md:px-4 md:left-[3.7rem] md:top-4
                   border border-slate-700 bg-white/1 absolute rounded-lg rotate-[-3.88deg] left-[-0.688rem] top-[1.208rem] backdrop-blur-sm py-2 px-3"
          >
            <p class="md:text-xl-normal text-base-normal text-slate-900">
              {{ $t('landing.chat_bubbles.first_time_organiser') }}
            </p>
          </div>

          <div
            class="md:py-3 md:px-4 md:left-[18.67rem] md:top-[1.313rem] md:rotate-[1.36deg]
                   border border-slate-700 bg-white/1 absolute rounded-lg rotate-[-2.78deg] z-20 backdrop-blur-sm left-[12.1rem] top-[5.087rem] py-2 px-3"
          >
            <p class="md:text-xl-normal text-base-normal text-slate-900">
              {{ $t('landing.chat_bubbles.club_owner') }}
            </p>
          </div>

          <div
            class="md:py-3 md:px-4 md:left-[11.69rem] md:rotate-[3.14deg] md:top-[4.439rem]
                   border border-slate-700 bg-white/1 absolute rounded-lg backdrop-blur-sm rotate-[-5.08deg] left-[7.362rem] top-[2.7rem] py-2 px-3"
          >
            <p class="md:text-xl-normal text-base-normal text-slate-900">
              {{ $t('landing.chat_bubbles.experienced_promoter') }}
            </p>
          </div>

          <div
            class="md:py-3 md:px-4 md:left-[-3.8rem] md:top-[4.408rem]
                   border border-slate-700 bg-white/1 absolute rounded-lg backdrop-blur-sm rotate-[3.75deg] left-[-0.284rem] top-[5.159rem] py-2 px-3 z-20"
          >
            <p class="md:text-xl-normal text-base-normal text-slate-900">
              {{ $t('landing.chat_bubbles.student_association') }}
            </p>
          </div>
        </div>

        <div class="flex flex-col md:gap-9 gap-3 justify-center items-center">
          <h1 class="md:text-8xl text-4xl font-sofia font-[800] text-pie-700 spacing-desktop-5 spacing-mobile-3">
            {{ $t('landing.solutions.title') }}
          </h1>
          <p class="md:text-xl-medium text-sm-medium text-slate-700 md:w-[36.688rem] text-center">
            {{ $t('landing.solutions.description') }}
          </p>
        </div>
      </div>

      <div class="flex flex-col justify-center items-center gap-6">
        <div class="md:flex md:flex-row grid grid-col-1 items-center justify-center gap-6 md:w-[80rem]">
          <!-- Simplified Ticketing -->
          <div
            class="md:h-[17.125rem] md:pt-9 md:px-8 md:pb-7 md:gap-9
                   p-6 flex-1 min-w-0 flex flex-col border border-slate-700 bg-slate-100 rounded-2xl shadow gap-4"
          >
            <Ticket02Icon :size="36" class="text-pie-700" />
            <div class="flex flex-col md:gap-2 gap-1">
              <h1 class="md:text-2xl-bold text-lg-bold text-slate-900">
                {{ $t('landing.solutions.simplified_ticketing.title') }}
              </h1>
              <p class="md:text-sm-normal text-xs-medium text-slate-500">
                {{ $t('landing.solutions.simplified_ticketing.description') }}
              </p>
            </div>
          </div>

          <!-- Integrated Payments & Compliance Aids -->
          <div
            class="md:h-[17.125rem] md:pt-9 md:px-8 md:pb-7 md:gap-9
                   p-6 flex-1 min-w-0 flex flex-col border border-slate-700 bg-slate-100 rounded-2xl shadow gap-4"
          >
            <EuroCircleIcon :size="36" class="text-pie-700" />
            <div class="flex flex-col md:gap-2 gap-1">
              <h1 class="md:text-2xl-bold text-lg-bold text-slate-900">
                {{ $t('landing.solutions.integrated_payment.title') }}
              </h1>
              <p class="md:text-sm-normal text-xs-medium text-slate-500">
                {{ $t('landing.solutions.integrated_payment.description') }}
              </p>
            </div>
          </div>

          <!-- Low-fee structure -->
          <div
            class="md:h-[17.125rem] md:pt-9 md:px-8 md:pb-7 md:gap-9
                   p-6 flex-1 min-w-0 flex flex-col border border-slate-700 bg-slate-100 rounded-2xl shadow gap-4"
          >
            <PaymentSuccess02Icon :size="36" class="text-pie-700" />
            <div class="flex flex-col md:gap-2 gap-1">
              <h1 class="md:text-2xl-bold text-lg-bold text-slate-900">
                {{ $t('landing.solutions.low_fee_structure.title') }}
              </h1>
              <p class="md:text-sm-normal text-xs-medium text-slate-500">
                {{ $t('landing.solutions.low_fee_structure.description') }}
              </p>
            </div>
          </div>
        </div>

        <div class="md:flex md:flex-row grid grid-col-1 items-center justify-center gap-6 md:w-[80rem]">
          <!-- Actionable Analytics -->
          <div
            class="md:h-[17.125rem] md:pt-9 md:px-8 md:pb-7 md:gap-9
                   p-6 flex-1 min-w-0 flex flex-col border border-slate-700 bg-slate-100 rounded-2xl shadow gap-4"
          >
            <ChartLineData01Icon :size="36" class="text-pie-700" />
            <div class="flex flex-col md:gap-2 gap-1">
              <h1 class="md:text-2xl-bold text-lg-bold text-slate-900">
                {{ $t('landing.solutions.actionable_analytics.title') }}
              </h1>
              <p class="md:text-sm-normal text-xs-medium text-slate-500">
                {{ $t('landing.solutions.actionable_analytics.description') }}
              </p>
            </div>
          </div>

          <!-- Reliable Support -->
          <div
            class="md:h-[17.125rem] md:pt-9 md:px-8 md:pb-7 md:gap-9
                   p-6 flex-1 min-w-0 flex flex-col border border-slate-700 bg-slate-100 rounded-2xl shadow gap-4"
          >
            <CustomerSupportIcon :size="36" class="text-pie-700" />
            <div class="flex flex-col md:gap-2 gap-1">
              <h1 class="md:text-2xl-bold text-lg-bold text-slate-900">
                {{ $t('landing.solutions.support.title') }}
              </h1>
              <p class="md:text-sm-normal text-xs-medium text-slate-500">
                {{ $t('landing.solutions.support.description') }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Festival types -->
    <div class="mb-10">
      <div class="bg-pie-700 flex rotate-[-2.5deg] w-[102vw] md:py-12 py-6 md:gap-12 gap-6 items-center justify-center">
        <div v-for="festival in festivals" :key="festival" class="md:gap-12 gap-6">
          <div class="flex md:gap-12 gap-6 items-center justify-center">
            <div class="md:w-12 md:h-12 w-6 h-6 bg-transparent rotate-[2.5deg]">
              <img src="assets/images/logo_white.png" alt="ALT">
            </div>
            <p class="md:text-4xl text-xl font-sofia font-[800] text-white whitespace-nowrap spacing-desktop-3 spacing-mobile-3">
              {{ festival }}
            </p>
          </div>
        </div>
        <img src="assets/images/logo_white.png" alt="ALT" class="md:w-12 md:h-12 w-6 h-6 rotate-[2.5deg]">
      </div>
    </div>

    <!-- Platform -->
    <div class="w-full flex flex-col justify-center items-center">
      <!-- Images -->
      <div class="md:h-[68.375rem] h-[46rem] md:w-[102rem] w-[20.5rem] relative">
        <div class="absolute md:w-[30rem] md:h-[22.25rem] w-[15.009rem] h-[11.144rem] bg-slate-300 rounded-2xl shadow border border-slate-700 md:left-[11.25rem] md:top-[0.009rem] overflow-hidden">
          <img src="assets/images/landing_dashboard.png" alt="ALT">
        </div>
        <div class="absolute md:w-[20.313rem] md:h-[13.125rem] w-[8.688rem] h-[5.563rem] bg-slate-300 rounded-2xl shadow border border-slate-700 md:left-[64.5rem] md:top-[16.563rem] top-9 left-[13.438rem] overflow-hidden">
          <img src="assets/images/landing_shkrabaantony.jpg" alt="ALT">
        </div>
        <div class="absolute md:w-[38.188rem] md:h-[32.125rem] w-[19.106rem] h-[16.074rem] bg-slate-300 rounded-2xl shadow border border-slate-700 md:left-[28.625rem] md:top-[6.75rem] top-[13.563rem] md:z-10 z-30 overflow-hidden">
          <img src="assets/images/landing_organiser_report.png" alt="ALT" class="w-full h-full object-cover">
        </div>
        <div class="absolute md:w-[35.438rem] md:h-[11.188rem] w-[17.733rem] h-[5.625rem] bg-slate-300 rounded-2xl shadow border border-slate-700 md:left-[55.188rem] md:top-[2.25rem] top-[8.625rem] left-11 z-20 overflow-hidden">
          <img src="assets/images/landing_promo_codes.png" alt="ALT">
        </div>
        <div class="absolute md:w-[44.938rem] md:h-[28.188rem] w-[16.903rem] h-[10.604rem] bg-slate-300 rounded-2xl shadow border border-slate-700 md:left-[45rem] md:top-[36.303rem] top-[26.912rem] left-[10.313rem] z-20 overflow-hidden">
          <img src="assets/images/landing_event.png" alt="ALT">
        </div>
        <!-- Desktop -->
        <NexButton text-key="landing.platform.publish_event" variant="primary" :append-icon="StartUp02Icon" :second-border="true" :paddingx="7" :paddingy="5" text-style="text-base-bold" class="absolute hidden md:inline-flex w-auto md:left-[64.5rem] md:top-[32.828rem] z-20" />
        <!-- Mobile -->
        <NexButton text-key="landing.platform.publish_event" variant="primary" :append-icon="StartUp02Icon" :second-border="true" :paddingx="3" :paddingy="2" text-style="text-sm-bold" second-border-inset="inset-[2px]" class="absolute md:hidden inline-flex w-auto top-[31.25rem] left-[0.563rem] z-20" />
        <Iphone class="absolute md:left-[16.5rem] md:top-[44.866rem] top-[34.866rem]" />
      </div>

      <div class="border border-slate-700 md:py-48 py-12 px-4 flex flex-col md:gap-3 w-full md:rounded-[3rem] rounded-3xl shadow-lg bg-white justify-center items-center relative z-30">
        <div class="md:w-[80rem] flex flex-col md:gap-20 gap-12 items-center justify-center">
          <div class="flex flex-col md:gap-9 gap-3 justify-center items-center">
            <h1 class="md:text-8xl text-4xl font-sofia font-[800] spacing-desktop-5 spacing-mobile-3 text-pie-700 text-center">
              {{ $t('landing.platform.title') }}
            </h1>
            <p class="md:text-xl-medium text-sm-medium text-slate-700 md:w-[36.688rem] text-center">
              {{ $t('landing.platform.description') }}
            </p>
          </div>
          <div class="flex flex-col md:gap-6 gap-4 justify-center items-center">
            <div class="flex flex-col md:flex-row md:gap-6 gap-6 justify-center items-stretch">
              <!-- Built-In Support Network -->
              <div
                class="md:flex-1 md:min-w-[30.125rem] md:px-9 md:pt-9 md:pb-7 md:gap-9
                       rounded-2xl bg-slate-100 border border-slate-700 flex flex-col shadow p-6 gap-4"
              >
                <Agreement01Icon :size="36" class="text-pie-700" />
                <div class="flex flex-col gap-2">
                  <!-- Desktop -->
                  <h1 class="text-2xl-bold text-slate-900 md:block hidden">
                    {{ $t('landing.platform.support_network.title') }}
                  </h1>
                  <p class="text-sm-normal text-slate-500 md:block hidden">
                    {{ $t('landing.platform.support_network.description') }}
                  </p>

                  <!-- Mobile -->
                  <h1 class="text-lg-bold text-slate-900 md:hidden">
                    {{ $t('landing.platform.support_network.title_mobile') }}
                  </h1>
                  <p class="text-xs-normal text-slate-500 md:hidden">
                    {{ $t('landing.platform.support_network.description_mobile') }}
                  </p>
                </div>
              </div>

              <!-- Built-In Support Network -->
              <div
                class="md:flex-1 md:min-w-[30.125rem] md:px-9 md:pt-9 md:pb-7 md:gap-9
                       rounded-2xl bg-slate-100 border border-slate-700 flex flex-col shadow p-6 gap-4"
              >
                <EuroSquareIcon :size="36" class="text-pie-700" />
                <div class="flex flex-col gap-2">
                  <!-- Desktop -->
                  <h1 class="text-2xl-bold text-slate-900 md:block hidden">
                    {{ $t('landing.platform.availability.title') }}
                  </h1>
                  <p class="text-sm-normal text-slate-500 md:block hidden">
                    {{ $t('landing.platform.availability.description') }}
                  </p>

                  <!-- Mobile -->
                  <h1 class="text-lg-bold text-slate-900 md:hidden">
                    {{ $t('landing.platform.availability.title') }}
                  </h1>
                  <p class="text-xs-normal text-slate-500 md:hidden">
                    {{ $t('landing.platform.availability.description') }}
                  </p>
                </div>
              </div>
            </div>

            <div class="w-full relative">
              <img src="assets/images/landing_pixels_maumascaro.jpg" alt="Placeholder" class="w-full md:h-[29.25rem] h-[31rem] bg-slate-600 md:rounded-2xl rounded-3xl">
              <div class="absolute bottom-0 md:p-16 p-6 flex md:flex-row gap-4 flex-col md:justify-between justify-center items-end">
                <div class="md:pr-11 md:w-2/3">
                  <h1 class="md:text-3xl-bold text-xl-bold text-white">
                    {{ $t('landing.platform.photo.title') }}
                  </h1>
                  <p class="md:text-lg-medium text-sm-medium text-slate-300">
                    {{ $t('landing.platform.photo.description') }}
                  </p>
                </div>
                <div class="flex md:justify-end justify-center w-full md:w-auto">
                  <NexButton text-key="landing.platform.photo.button" to="/contact" variant="secondary" border-color="slate-300" text-color="slate-300" :paddingx="6" :paddingy="3" text-style="text-lg-bold" class="w-full md:w-auto" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pricing -->
    <div class="md:my-11">
      <div class="md:py-48 py-12 md:w-[105vw] w-screen md:rotate-[2.5deg] bg-pie-700 flex justify-center items-center px-4">
        <div class="flex flex-col gap-20 justify-center items-center">
          <div class="flex flex-col gap-10 justify-center items-center">
            <h1 class="md:text-8xl text-5xl font-sofia font-[800] text-center text-white spacing-desktop-5 spacing-mobile-3">
              {{ $t('landing.pricing.title') }}
            </h1>
            <p class="md:text-xl-medium text-base-medium text-slate-300 md:w-[36.688rem] text-center">
              {{ $t('landing.pricing.description') }}
            </p>
          </div>

          <div class="flex md:flex-row flex-col md:gap-6 gap-4 w-full md:w-[68.5rem]">
            <!-- No fees -->
            <div class="rounded-2xl flex flex-col gap-9 justify-center items-center bg-slate-100 border border-slate-700 md:pt-9 md:px-9 md:pb-7 py-6 px-4 shadow md:flex-1">
              <div class="flex flex-col gap-2 justify-center items-center">
                <h1 class="md:text-9xl-bold text-6xl-bold text-slate-900">
                  0<span class="text-4xl-bold">€</span>
                </h1>
                <p class="md:text-xl-normal text-sm-normal text-slate-500">
                  {{ $t('landing.pricing.no_fees') }}
                </p>
              </div>
            </div>

            <!-- VAT -->
            <div class="rounded-2xl flex flex-col gap-9 justify-center items-center bg-slate-100 border border-slate-700 md:pt-9 md:px-9 md:pb-7 py-6 px-4 shadow md:flex-1">
              <div class="flex flex-col gap-2 justify-center items-center">
                <h1 class="md:text-9xl-bold text-6xl-bold text-slate-900">
                  6<span class="text-4xl-bold">%</span>
                </h1>
                <p class="md:text-xl-normal text-sm-normal text-slate-500">
                  {{ $t('landing.pricing.vat') }}
                </p>
              </div>
            </div>

            <!-- Studen discount -->
            <div class="rounded-2xl flex flex-col gap-9 justify-center items-center bg-slate-100 border border-slate-700 md:pt-9 md:px-9 md:pb-7 py-6 px-4 shadow md:flex-1">
              <div class="flex flex-col gap-2 justify-center items-center">
                <h1 class="md:text-9xl-bold text-6xl-bold text-slate-900">
                  50<span class="text-4xl-bold">%</span>
                </h1>
                <p class="md:text-xl-normal text-sm-normal text-slate-500">
                  {{ $t('landing.pricing.student_discount') }}
                </p>
              </div>
            </div>
          </div>
          <p class="md:text-xl-medium text-base-medium text-slate-300 md:w-[36.688rem] text-center">
            {{ $t('landing.pricing.first_event_discount_1') }}
            <span class="underline">{{ $t('landing.pricing.first_event_discount_2') }}</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Questions -->
    <div class="flex flex-col justify-center items-center gap-3 border border-slate-700 bg-white rounded-[3rem] shadow-lg md:py-48 py-12 px-4 w-full">
      <div class="flex flex-col justify-center items-center gap-[4.5rem] md:w-[53rem]">
        <div class="flex flex-col gap-9 justify-center items-center">
          <h1 class="md:text-8xl text-4xl font-sofia font-[800] text-pie-700 spacing-desktop-5 spacing-mobile-3">
            {{ $t('landing.questions.title') }}
          </h1>
          <p class="md:text-xl-medium text-sm-medium text-slate-700 md:w-[36.688rem] text-center">
            {{ $t('landing.questions.description') }}
          </p>
        </div>
        <div class="flex flex-col gap-6 justify-center items-center w-full">
          <div
            v-for="(faq, index) in faqData"
            :key="index"
            class="border-b border-slate-900 w-full"
          >
            <button
              class="py-6 flex justify-between w-full text-left hover:bg-slate-50 transition-colors duration-200"
              @click="toggleFaq(index)"
            >
              <p class="md:text-2xl-medium text-sm-medium text-slate-900 pr-4">
                {{ faq.question }}
              </p>
              <component
                :is="expandedFaq === index ? MinusSignSquareIcon : PlusSignSquareIcon"
                :size="25"
                class="text-slate-900 stroke-2 flex-shrink-0"
              />
            </button>
            <div
              v-if="expandedFaq === index"
              class="pb-6 transition-all duration-300 ease-in-out"
            >
              <p class="md:text-lg-normal text-sm-normal text-slate-600 leading-relaxed">
                {{ faq.answer }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Simplify Ticketing -->
    <div class="my-11">
      <div class="flex flex-col justify-center items-center md:px-52 md:py-24 gap-12 md:rotate-[-2.5deg] md:border-b md:border-t border-slate-700 backdrop-blur-sm md:w-[105vw] w-[18.5rem]">
        <div class="flex flex-col md:gap-6 gap-3 md:w-[55.813rem] justify-center items-center">
          <h1 class="md:text-5xl-bold text-2xl-bold text-slate-900 text-center">
            {{ $t('landing.simplify_ticketing.title') }}
          </h1>
          <p class="md:text-xl-medium text-sm-medium text-slate-700 text-center">
            {{ $t('landing.simplify_ticketing.description') }}
          </p>
        </div>
        <div class="flex gap-6 flex-col w-full md:flex-row justify-center items-center">
          <!-- Desktop -->
          <NexButton variant="primary" text-key="landing.simplify_ticketing.buttons.get_started" to="/registration" :second-border="true" :paddingx="9" :paddingy="6" class="md:flex hidden" />
          <NexButton variant="secondary" text-key="landing.simplify_ticketing.buttons.demo" to="/contact" :paddingx="9" :paddingy="6" :append-icon="PlayIcon" class="md:flex hidden" />
          <!-- Mobile -->
          <NexButton variant="primary" text-key="landing.simplify_ticketing.buttons.get_started" to="/registration" :second-border="true" :paddingx="12" :paddingy="4" text-style="text-base-bold" :full-width="true" class="md:hidden flex" />
          <NexButton variant="secondary" text-key="landing.simplify_ticketing.buttons.demo_mobile" to="/contact" :paddingx="12" :paddingy="4" :append-icon="PlayIcon" text-style="text-base-bold" :full-width="true" class="md:hidden flex" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@media (min-width: 768px) {
  .spacing-desktop-5 {
    letter-spacing: -0.05em;
  }
  .spacing-desktop-2{
    letter-spacing: -0.02em;
  }
  .spacing-desktop-3{
    letter-spacing: -0.03em;
  }
}

@media (max-width: 768px) {
  .spacing-mobile-5 {
    letter-spacing: -0.05em;
  }
  .spacing-mobile-2{
    letter-spacing: -0.02em;
  }
  .spacing-mobile-3{
    letter-spacing: -0.03em;
  }
}
</style>
