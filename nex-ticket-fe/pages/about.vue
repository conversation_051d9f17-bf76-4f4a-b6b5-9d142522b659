<script setup lang="ts">
import { FavouriteIcon, GlobalIcon, KnightShieldIcon, Search02Icon, SparklesIcon, WavingHand01Icon } from 'hugeicons-vue'

const { t } = useI18n()
</script>

<template>
  <div class="text-slate-800 overflow-hidden md:overflow-visible flex flex-col gap-40 justify-center items-center p-4">
    <!-- Hero -->
    <section class="md:w-[57.188rem] w-[20.5rem] py-20 md:py-28 text-center">
      <div class="md:w-[57.188rem] container mx-auto px-4 md:px-6 flex flex-col content-center items-center">
        <div>
          <h1
            class="md:text-8xl md:mb-4 spacing-desktop-5
                   text-5xl font-sofia text-pie-700 font-[800] spacing-mobile-3"
          >
            {{ t('info.aboutUs.hero.title') }}
          </h1>
          <p
            class="md:text-xl-medium
                 text-base-medium text-slate-700 max-w-3xl mx-auto mb-8 whitespace-pre-line"
          >
            {{ t('info.aboutUs.hero.subtitle') }}
          </p>
        </div>
        <div class="flex gap-6 flex-col md:flex-row md:w-auto w-full">
          <!-- Desktop -->
          <NexButton
            variant="primary"
            text-key="info.aboutUs.buttons.getStartedForFree"
            to="/registration.vue"
            :second-border="true"
            :paddingx="9"
            :paddingy="6"
            class="hidden md:flex"
          />
          <NexButton
            variant="secondary"
            text-key="info.aboutUs.buttons.contactSales"
            :append-icon="WavingHand01Icon"
            :paddingx="9"
            :paddingy="6"
            class="hidden md:flex"
          />

          <!-- Mobile -->
          <NexButton
            variant="primary"
            text-key="info.aboutUs.buttons.getStartedForFree"
            :second-border="true"
            :paddingx="6"
            :paddingy="4"
            class="md:hidden"
          />
          <NexButton
            variant="secondary"
            text-key="info.aboutUs.buttons.contactSales"
            :append-icon="WavingHand01Icon"
            :paddingx="6"
            :paddingy="4"
            class="md:hidden"
          />
        </div>
      </div>
    </section>

    <!-- Mission -->
    <section class="md:pb-9">
      <div
        class="md:w-[120vw] md:-ml-[10vw] md:rotate-[2.5deg] md:py-40 md:px-0 md:gap-12
               w-screen flex items-center justify-center gap-3 bg-pie-700 py-12 px-4"
      >
        <div class="md:w-[80rem] ">
          <div class="flex flex-col gap-6 w-[22.5rem] md:w-auto">
            <div class="px-4 md:px-0">
              <p
                class="md:text-2xl spacing-desktop-2
                       text-base font-sofia text-slate-300 font-[800] spacing-mobile-2"
              >
                {{ t('info.aboutUs.mission.title') }}
              </p>
              <p
                class="md:text-4xl-medium
                       text-xl-medium text-white"
              >
                “{{ t('info.aboutUs.mission.description') }}“
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="md:w-full w-[20.5rem]">
      <div class="flex items-center justify-center rounded-[3rem] border border-slate-700 md:py-48 md:px-0 py-12 px-4 bg-white">
        <div class="md:w-[80rem] w-full flex flex-col justify-center text-center items-center gap-3">
          <!-- Desktop -->
          <h1
            class="md:text-8xl spacing-desktop-5 md:block
                   text-4xl font-sofia font-[800] text-pie-700 hidden"
          >
            {{ t('info.aboutUs.values.title') }}
          </h1>

          <p class="text-xl-medium text-slate-500 md:block hidden">
            {{ t('info.aboutUs.values.description') }}
          </p>

          <!-- Mobile -->
          <h1 class="md:hidden text-4xl font-sofia font-[800] text-pie-700 spacing-mobile-3">
            {{ t('info.aboutUs.values.mobileTitle') }}
          </h1>

          <p class="text-sm-medium text-slate-700 md:hidden">
            {{ t('info.aboutUs.values.mobileDescription') }}
          </p>

          <div class="flex md:flex-row flex-col gap-6 md:mt-20">
            <!-- Simplicity -->
            <div
              class="md:min-w-[15.875rem] md:h-[15.125rem] md:pt-9 md:pb-7 md:px-9 md:gap-9
                    gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <SparklesIcon :size="36" class="text-pie-700" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="md:text-2xl-bold
                         text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.simplicity.title') }}
                </p>
                <p class="md:text-sm-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.simplicity.description') }}
                </p>
              </div>
            </div>

            <!-- Transparency -->
            <div
              class="md:min-w-[15.875rem] md:h-[15.125rem] md:pt-9 md:pb-7 md:px-9 md:gap-9
                    gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <Search02Icon :size="36" class="text-pie-700" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="md:text-2xl-bold
                         text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.transparency.title') }}
                </p>
                <p class="md:text-sm-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.transparency.description') }}
                </p>
              </div>
            </div>

            <!-- Community -->
            <div
              class="md:min-w-[15.875rem] md:h-[15.125rem] md:pt-9 md:pb-7 md:px-9 md:gap-9
                    gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <GlobalIcon :size="36" class="text-pie-700" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="md:text-2xl-bold
                         text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.community.title') }}
                </p>
                <p class="md:text-sm-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.community.description') }}
                </p>
              </div>
            </div>
          </div>

          <div class="flex md:flex-row flex-col gap-6 w-full">
            <!-- Passion -->
            <div
              class="md:min-w-[15.875rem] md:h-[15.125rem] md:pt-9 md:pb-7 md:px-9 md:gap-9
                    gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <FavouriteIcon :size="36" class="text-pie-700" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="md:text-2xl-bold
                         text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.passion.title') }}
                </p>
                <p class="md:text-sm-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.passion.description') }}
                </p>
              </div>
            </div>

            <!-- Reliability -->
            <div
              class="md:min-w-[15.875rem] md:h-[15.125rem] md:pt-9 md:pb-7 md:px-9 md:gap-9
                    gap-4 p-6 flex flex-col flex-1 min-w-0 rounded-2xl border border-slate-700 shadow-sm bg-slate-100"
            >
              <KnightShieldIcon :size="36" class="text-pie-700" />
              <div class="text-left flex flex-col gap-2">
                <p
                  class="md:text-2xl-bold
                         text-lg-bold text-slate-700"
                >
                  {{ t('info.aboutUs.values.reliability.title') }}
                </p>
                <p class="md:text-sm-medium text-xs-medium text-slate-500">
                  {{ t('info.aboutUs.values.reliability.description') }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="flex justify-center">
      <div class="flex flex-col md:flex-row md:py-12 w-[20.5rem] md:w-[80rem] md:gap-[6.313rem] gap-9 items-center justify-center">
        <img
          class="shrink-0 rounded-2xl w-[20.5rem] h-[26.313rem] md:w-[22.438rem] md:h-[34.438rem] object-cover bg-slate-200"
          src="assets/images/marek_pospisil.jpg"
          alt="Placeholder"
        >
        <div class="flex flex-col gap-4">
          <p class="md:text-4xl-medium text-xl-medium text-slate-900">
            “
            <span class="font-sofia text-pie-700">
              TICKETPIE
            </span>
            <span>
              {{ t('info.aboutUs.story.paragraph1') }}
            </span>
            ”
          </p>
          <div>
            <p class="md:text-2xl-bold text-sm-bold text-slate-900">
              {{ t('info.aboutUs.story.names') }}
            </p>
            <p class="md:text-2xl-medium text-sm-normal text-slate-500">
              {{ t('info.aboutUs.story.founders') }}
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
@media (min-width: 768px) {
  .spacing-desktop-5 {
    letter-spacing: -0.05em;
  }
  .spacing-desktop-2{
    letter-spacing: -0.02em;
  }
  .spacing-desktop-3{
    letter-spacing: -0.03em;
  }
}

@media (max-width: 768px) {
  .spacing-mobile-5 {
    letter-spacing: -0.05em;
  }
  .spacing-mobile-2{
    letter-spacing: -0.02em;
  }
  .spacing-mobile-3{
    letter-spacing: -0.03em;
  }
}
</style>
