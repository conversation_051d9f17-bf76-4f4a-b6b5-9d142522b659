<script lang="ts" setup>
import { SentIcon } from 'hugeicons-vue'
import { da } from 'vuetify/lib/locale/index.mjs'
import { boolean as yupBoolean, object as yupObject, string as yupString } from 'yup'

const { t } = useI18n()

const schema = computed(() => {
  return toTypedSchema(
    yupObject({
      name: yupString().required().min(5).max(25).label(t('auth.fields.name')),
      email: yupString().email().required().label(t('auth.fields.email')),
      subject: yupString().required().min(5).max(20).label(t('auth.fields.subject')),
      message: yupString().required().min(10).max(2500).label(t('auth.fields.contact_message')),
      user_agreement: yupBoolean().required().oneOf([true]),
    }),
  )
})

const feedback = useFeedback()

const { validate } = useForm({
  validationSchema: schema,
})

const name = useField<string>('name')
const email = useField<string>('email')
const subject = useField<string>('subject')
const message = useField<string>('message')
const user_agreement = useField<boolean>('user_agreement')
const serverResponse = ref<null | any>(null)

const validatedInput = ref<boolean>(false)

async function sendMessage() {
  const isValid = await validate()
  validatedInput.value = true
  if (isValid.valid) {
    try {
      const { error: fetchError } = await useAPI('/api/public/contact_support', {
        method: 'POST',
        body: {
          name: name.value.value,
          email: email.value.value,
          subject: subject.value.value,
          message: message.value.value,
        },
      })
      if (fetchError.value) {
        feedback.error('Error creating event:', { level: 'error', rollbar: true, extras: fetchError.value })
        serverResponse.value = fetchError.value.message
      }
    }
    catch (err: any) {
      feedback.error('Unexpected error:', { level: 'error', rollbar: true, extras: err })
      serverResponse.value = err.message
    }
  }
}
</script>

<template>
  <div style="display:none">
    <!-- This is a commented out code to not delete comments that are inside. To display this page delete this prior div and /div at the end of the page -->
    <div class="flex flex-col gap-8 md:gap-16">
      <div
        class="lg:flex-row lg:max-w-[80rem]
             flex flex-col w-full justify-center items-start p-3 gap-6 bg-pie-25 border border-slate-400 shadow-2xl rounded-3xl max-h-screen overflow-auto" @click.stop
      >
        <!-- Message -->
        <div
          class="lg:gap-5 lg:h-full
              flex flex-col items-start w-full gap-8 p-9 order-0 "
        >
          <span
            class="lg:items-center lg:gap-0 lg:text-3xl-bold
                text-2xl-bold flex flex-row items-start text-pie-700 z-10 isolate gap-9"
          >
            {{ t('info.contact.contact_ticketpie_support') }}
          </span>
          <!-- Details -->
          <div class="flex flex-col items-start gap-2 w-full">
            <div class="text-base-medium text-slate-500 ">
              {{ t('info.contact.details') }}
            </div>
            <div
              class="lg:gap-2
                  flex flex-col items-start w-full order-1 gap-4"
            >
              <div
                class="md:flex-row
                    flex flex-col items-start gap-4 w-full order-1 "
              >
                <!-- Name -->
                <v-text-field
                  v-model="name.value.value"
                  class="lg:w-1/2 w-full"
                  variant="outlined"
                  :label="t('auth.fields.name')"
                  append-inner-icon="mdi-account"
                  :error-messages="name.errorMessage.value"
                />
                <!-- Email -->
                <v-text-field
                  v-model="email.value.value"
                  class="lg:w-1/2 w-full"
                  variant="outlined"
                  :label="t('auth.fields.email')"
                  append-inner-icon="mdi-email"
                  :error-messages="email.errorMessage.value"
                />
              </div>
              <div class="flex flex-row items-center gap-2 w-full order-1 flex-grow-0">
                <!-- Subject -->
                <v-text-field
                  v-model="subject.value.value"
                  variant="outlined"
                  :label="t('auth.fields.subject')"
                  :error-messages="subject.errorMessage.value"
                />
              </div>
            </div>
          </div>
          <!-- Message -->
          <div class="flex flex-col items-start gap-2 w-full order-1 flex-grow">
            <div class="text-base-medium text-slate-500">
              {{ t('info.contact.message') }}
            </div>
            <v-textarea
              id="message"
              v-model="message.value.value"
              :label="t('auth.fields.contact_message')"
              :error-messages="message.errorMessage.value"
              variant="outlined"
              placeholder="Enter your message"
              class="flex items-center w-full h-full min-h-32 order-1"
            />
          </div>
          <div class="flex flex-col items-start gap-2 w-full order-1 flex-grow">
            <NexCheckbox
              id="user_agreement"
              v-model="user_agreement.value.value"
              :text="t('public.event_info.contact_form.user_agreement')"
              :validated="validatedInput"
              :required="true"
            />
          </div>

          <!-- Button -->
          <div class="flex justify-center items-start gap-3 isolation bg-pie-700 rounded-lg order-2">
            <div
              class="lg:flex
              p-1 gap-3 bg-pie-700 rounded-lg hover:bg-pie-600 justify-center items-center w-full"
            >
              <div
                class="lg:px-14
            border border-white rounded-lg py-2 px-12 place-items-center w-full justify-center"
              >
                <button class="bg-transparent text-white text-lg-bold text-center w-full text-nowrap" @click="sendMessage">
                  <div class="flex flex-row place-items-center gap-3 justify-center">
                    {{ $t('public.event_info.contact_form.buttons.send') }}
                    <SentIcon />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 px-4 gap-12">
        <div class="flex flex-col gap-4">
          <div class="text-base-bold text-slate-600">
            {{ t('info.contact.title') }}
          </div>
          <div class="text-base-medium text-slate-500">
            placeholder for email and info and stuff
          </div>
        </div>
        <div class="flex md:justify-end">
          <div class="flex flex-col gap-4">
            <div class="text-base-bold text-slate-600">
              {{ t('info.contact.title') }}
            </div>
            <div class="text-base-medium text-slate-500">
              placeholder for email and info and stuff
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>

</style>
