import { description } from '@nuxt/scripts/dist/runtime/validation/valibot'
import { FirstAidKitIcon } from 'hugeicons-vue'

export default {
  landing: {
    hero: {
      title: 'EFFORTLESS \n EVENT MANAGEMENT.',
      description: 'Bring your event ideas to life with unparalleled ease and efficiency.',
      get_started_for_free: 'Get started for free',
      contact_sales: 'Contact sales',
    },
    photo: {
      title: 'Launch your event in minutes',
      description_1: 'We made it really simple to host, launch and manage events on out platform. Combined with our ',
      description_2: 'low fees,',
      description_3: ' there is no stopping you from making the best events.',
      try: 'Try it out',
    },
    chat_bubbles: {
      first_time_organiser: '🥳 First time organiser?',
      club_owner: '🎊 Club owner?',
      experienced_promoter: '😎 Experienced promoter?',
      student_association: '👩‍🎓 Student association?',
    },
    solutions: {
      title: 'WE HAVE THE SOLUTION.',
      description: 'The undisputed easiest way to set up and manage an event. Go from idea to selling tickets in minutes.',
      simplified_ticketing: {
        title: 'Simplified Ticketing',
        description: 'An incredibly easy-to-use interface for setting up event pages and managing ticket sales. Sell multiple ticket types with ease (GA, VIP, Early Bird, etc.).',
      },
      integrated_payment: {
        title: 'Integrated Payments & Compliance Aids',
        description: 'Secure payment processing with tools designed to significantly ease the burden of financial tracking and VAT compliance.',
      },
      low_fee_structure: {
        title: 'Low-Fee Structure',
        description: 'Transparent and minimal fees, making professional ticketing accessible to all budgets.',
      },
      actionable_analytics: {
        title: 'Actionable Analytics',
        description: 'Clear, easy-to-understand dashboards providing insights into sales and attendees. Gain valuable insights to grow future events.',
      },
      support: {
        title: 'Reliable Support',
        description: 'Our team is always available to help with any questions or issues .',
      },
    },
    platform: {
      title: 'MORE THAN JUST A PLATFORM.',
      description: 'We guide and help you to get started with organising the events you want.',
      publish_event: 'Publish event',
      support_network: {
        title: 'Built-In Support Network',
        title_mobile: 'Reliable Support',
        description: 'We help you with finding reliable collaborators. Through our platform you can get in touch with venues, security, promoters, DJs and others.',
        description_mobile: 'Our team is always available to help with any questions or issues .',
      },
      availability: {
        title: 'Ticketpie is available in all the EU',
        description: 'We support hosting events in major cities across the whole European Union.',
      },
      photo: {
        title: 'We’re here to help',
        description: 'Our team is available to help with any questions or issues and facilitating the process of hosting your event.',
        button: 'Get in touch',
      },
    },
    pricing: {
      title: 'CRYSTAL CLEAR PRICING.',
      description: 'Keep more of what you earn with straightforward and competitive pricing.',
      no_fees: 'No fees for free events',
      vat: 'Per ticket + VAT',
      student_discount: 'Discount for students',
      first_event_discount_1: 'Oh. And your first event is on us!',
      first_event_discount_2: 'You can start for free now.',
    },
    questions: {
      title: 'GOT QUESTIONS?',
      description: 'The undisputed easiest way to set up and manage an event. Go from idea to selling tickets in minutes.',
      question_text: 'What is this question about?',
    },
    simplify_ticketing: {
      title: 'Ready to Simplify Your Ticketing & Amplify Your Success?',
      description: 'Join hundreds of successful organizers who trust TicketPie to power their events. It\'s easy to get started, and your first event setup is on us!',
      buttons: {
        get_started: 'Get started',
        demo: 'Request a personalised demo',
        demo_mobile: 'Request a demo',
      },
    },
  },
}
